const Koa = require('koa');
const app = new Koa();

// logger

app.use(async (ctx, next) => {
  console.log(1, ctx.ip)
  console.log(11, ctx.request.ip)
  await next();
  console.log(2)
  const rt = ctx.response.get('X-Response-Time');
  console.log(`${ctx.method} ${ctx.url} - ${rt}`);
  console.log(3)
});

// x-response-time

app.use(async (ctx, next) => {
    console.log(4)
  const start = Date.now();
  await next();
  console.log(5)
  const ms = Date.now() - start;
  ctx.set('X-Response-Time', `${ms}ms`);
  console.log(6)
});

// response

app.use(async ctx => {
  console.log(7)
  ctx.body = 'Hello World';
});

// 打印 1、4、7、5、6、2、3
app.listen(3000);